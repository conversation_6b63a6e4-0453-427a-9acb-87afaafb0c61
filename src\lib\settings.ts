import fs from "fs/promises";
import path from "path";

// Optional: import your DB client (like <PERSON><PERSON><PERSON> or <PERSON>rism<PERSON>)
import { db } from "@/lib/db"; // adjust this to your setup

// If you’re using Cloudflare Workers or KV
declare const APP_SETTINGS: KVNamespace;

// For Vercel Edge Config (optional)
import { get, set } from "@vercel/edge-config";

// --------------------------------------
// Configuration
// --------------------------------------
const SETTINGS_KEY = "site:settings";
const SETTINGS_FILE = path.join(process.cwd(), "config/settings.json");

type SettingsBackend = "kv" | "vercel" | "file" | "db";
const backend = (process.env.SETTINGS_BACKEND || "file") as SettingsBackend;

// Optional: in-memory cache to reduce latency
let cache: any = null;
let cacheTime = 0;
const CACHE_TTL_MS = 60_000; // 1 minute cache

// --------------------------------------
// Public API
// --------------------------------------
export async function getSettings(force = false): Promise<any> {
  if (!force && cache && Date.now() - cacheTime < CACHE_TTL_MS) {
    return cache;
  }

  let settings: any = {};

  switch (backend) {
    case "kv":
      settings = await getFromKV();
      break;
    case "vercel":
      settings = await getFromVercelEdgeConfig();
      break;
    case "file":
      settings = await getFromFile();
      break;
    case "db":
      settings = await getFromDB();
      break;
    default:
      throw new Error(`Unknown settings backend: ${backend}`);
  }

  cache = settings;
  cacheTime = Date.now();
  return settings;
}

export async function updateSettings(newSettings: any): Promise<void> {
  cache = newSettings;
  cacheTime = Date.now();

  switch (backend) {
    case "kv":
      await saveToKV(newSettings);
      break;
    case "vercel":
      await saveToVercelEdgeConfig(newSettings);
      break;
    case "file":
      await saveToFile(newSettings);
      break;
    case "db":
      await saveToDB(newSettings);
      break;
    default:
      throw new Error(`Unknown settings backend: ${backend}`);
  }
}

// --------------------------------------
// Adapters
// --------------------------------------
async function getFromKV() {
  try {
    const data = await APP_SETTINGS.get(SETTINGS_KEY, "json");
    return data || {};
  } catch (err) {
    console.error("KV read error:", err);
    return {};
  }
}

async function saveToKV(settings: any) {
  await APP_SETTINGS.put(SETTINGS_KEY, JSON.stringify(settings));
}

async function getFromVercelEdgeConfig() {
  const data = await get(SETTINGS_KEY);
  return data || {};
}

async function saveToVercelEdgeConfig(settings: any) {
  // Edge Config is read-only from runtime. You can only edit via dashboard/API.
  console.warn("Vercel Edge Config cannot be updated at runtime. Use dashboard/API.");
}

async function getFromFile() {
  try {
    const json = await fs.readFile(SETTINGS_FILE, "utf-8");
    return JSON.parse(json);
  } catch {
    return {};
  }
}

async function saveToFile(settings: any) {
  await fs.writeFile(SETTINGS_FILE, JSON.stringify(settings, null, 2));
}

async function getFromDB() {
  try {
    const record = await db.settings.findUnique({ where: { key: SETTINGS_KEY } });
    return record?.value || {};
  } catch (err) {
    console.error("DB read error:", err);
    return {};
  }
}

async function saveToDB(settings: any) {
  await db.settings.upsert({
    where: { key: SETTINGS_KEY },
    update: { value: settings },
    create: { key: SETTINGS_KEY, value: settings },
  });
}
